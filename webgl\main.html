<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>白云电气设备数字孪生系统</title>
    <link rel="shortcut icon" href="TemplateData/favicon.ico">
    <link rel="stylesheet" href="styles.css">
    <!-- 引入 echarts -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <!-- 引入图表配置文件 -->
    <script src="charts.js"></script>
  </head>
  <body>
    <div id="main-container">
      <!-- 顶部标题栏 -->
      <div id="header">
        <div id="title">白云电气设备数字孪生系统</div>
        <div id="controls">
          <button class="control-btn" id="overview-btn">总览</button>
          <button class="control-btn" id="tour-btn">漫游</button>
          <button class="control-btn" id="expand-btn">展开</button>
        </div>
      </div>
      
      <!-- 内容区域 -->
      <div id="content">
        <!-- Unity WebGL 嵌入容器 -->
        <div id="unity-embed-container">
          <iframe id="unity-iframe" src="index.html" frameborder="0"></iframe>
        </div>
        
        <!-- 图表容器 -->
        <div id="chart1" class="chart-container">
          <div class="chart-title">设备运行状态</div>
          <div id="chart1-content" class="chart"></div>
        </div>
        
        <div id="chart2" class="chart-container">
          <div class="chart-title">温度监测</div>
          <div id="chart2-content" class="chart"></div>
        </div>
        
        <div id="chart3" class="chart-container">
          <div class="chart-title">电压监测</div>
          <div id="chart3-content" class="chart"></div>
        </div>
        
        <div id="chart4" class="chart-container">
          <div class="chart-title">系统负载</div>
          <div id="chart4-content" class="chart"></div>
        </div>
      </div>
    </div>
    
    <script>
      /**
       * 白云电气设备数字孪生系统 - 主页面脚本
       * 处理Unity WebGL嵌入和图表显示
       */
      
      var unityInstance = null;
      var unityIframe = null;
      
      // 页面加载完成后初始化
      window.addEventListener("load", function () {
        initMainPage();
      });
      
      /**
       * 初始化主页面
       */
      function initMainPage() {
        unityIframe = document.getElementById('unity-iframe');

        // 监听来自Unity iframe的消息
        window.addEventListener('message', function(event) {
          // 确保消息来源安全
          if (event.source !== unityIframe.contentWindow) {
            return;
          }

          if (event.data && event.data.type === 'unityLoaded') {
            console.log('Unity WebGL 加载完成');
            onUnityLoaded();
          }
        });

        // 绑定控制按钮事件
        bindControlEvents();

        // 绑定窗口大小变化事件
        window.addEventListener('resize', function() {
          resizeAllCharts();
        });
      }
      
      /**
       * Unity加载完成后的回调
       */
      function onUnityLoaded() {
        // 初始化图表
        initCharts();
      }
      
      /**
       * 绑定控制按钮事件
       */
      function bindControlEvents() {
        // 总览按钮
        document.getElementById("overview-btn").addEventListener("click", function() {
          sendUnityCommand("Main Camera", "SwitchToOverviewPosition");
          // 显示所有图表容器
          var chartContainers = document.querySelectorAll('.chart-container');
          chartContainers.forEach(function(container) {
            container.style.display = 'block';
          });
        });
        
        // 漫游按钮
        document.getElementById("tour-btn").addEventListener("click", function() {
          sendUnityCommand("Main Camera", "ToggleDeviceViewTour");
          // 隐藏所有图表容器
          var chartContainers = document.querySelectorAll('.chart-container');
          chartContainers.forEach(function(container) {
            container.style.display = 'none';
          });
        });
        
        // 展开/收起按钮
        document.getElementById("expand-btn").addEventListener("click", function() {
          sendUnityCommand("Device", "ToggleExpand");
          
          // 切换按钮文本
          var expandBtn = document.getElementById("expand-btn");
          if (expandBtn.textContent === "展开") {
            expandBtn.textContent = "收起";
          } else {
            expandBtn.textContent = "展开";
          }
        });
      }
      
      /**
       * 向Unity发送命令
       * @param {string} target - 目标GameObject名称
       * @param {string} method - 要调用的方法名
       * @param {string} parameter - 可选参数
       */
      function sendUnityCommand(target, method, parameter) {
        try {
          if (unityIframe && unityIframe.contentWindow) {
            unityIframe.contentWindow.postMessage({
              type: 'unityCommand',
              target: target,
              method: method,
              parameter: parameter || ''
            }, '*');
            console.log('发送Unity命令:', target, method, parameter);
          } else {
            console.warn('Unity iframe 未准备就绪');
          }
        } catch (error) {
          console.error('发送Unity命令失败:', error);
        }
      }
    </script>
  </body>
</html>
